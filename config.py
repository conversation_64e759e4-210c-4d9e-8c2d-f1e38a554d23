#!/usr/bin/env python3
"""
EXTRACTEUR DE CONTRATS SIMPLE ET EFFICACE - VERSION AMÉLIORÉE
Extraction optimisée avec classification intelligente des informations
"""

import os
import json
import pytesseract
from pathlib import Path
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional, Tuple

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Dossiers
PDF_FOLDER = 'contrat'
IMG_FOLDER = 'images'
TEXT_FOLDER = 'ocr_texts'
RESULT_FOLDER = 'results'

class AdvancedContractExtractor:
    """Extracteur avancé pour contrats avec classification intelligente"""
    
    def __init__(self):
        self.ocr_config = r'--oem 3 --psm 6'
        self.confidence_threshold = 0.7
        
    def extract_text_from_pdf(self, pdf_path: str, pdf_name: str) -> str:
        """Extraction OCR optimisée"""
        logger.info(f"📄 Extraction OCR pour {pdf_name}")
        
        os.makedirs(IMG_FOLDER, exist_ok=True)
        os.makedirs(TEXT_FOLDER, exist_ok=True)
        
        pages = convert_from_path(pdf_path, dpi=300, fmt='PNG')
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Page {page_num}/{len(pages)}...")
            
            enhanced_page = self.enhance_image_advanced(page)
            
            img_path = f"{IMG_FOLDER}/{pdf_name}_page_{page_num}.png"
            enhanced_page.save(img_path, "PNG")
            
            text = pytesseract.image_to_string(enhanced_page, lang='fra+eng', config=self.ocr_config)
            
            text_path = f"{TEXT_FOLDER}/{pdf_name}_page_{page_num}.txt"
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(text)
            
            all_text += f"\n--- Page {page_num} ---\n{text}"
        
        return self.clean_text_advanced(all_text)
    
    def enhance_image_advanced(self, image: Image.Image) -> Image.Image:
        """Amélioration avancée de l'image"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage adaptatif
        denoised = cv2.fastNlMeansDenoising(img_array, None, 10, 7, 21)
        
        # Amélioration du contraste adaptatif
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation optimisée
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphologie pour nettoyer
        kernel = np.ones((1,1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return Image.fromarray(cleaned)
    
    def clean_text_advanced(self, text: str) -> str:
        """Nettoyage avancé du texte OCR"""
        if not text:
            return ""

        # Corrections OCR spécialisées
        corrections = {
            r'[|]': 'I',
            r'[0O](?=[A-Z])': '0',
            r'(?<=[0-9])[O](?=[0-9])': '0',
            r'[1l](?=[0-9]{2,})': '1',
            r'(?<=[0-9])[l](?=[0-9])': '1',
            r'rn': 'm',
            r'cl': 'd',
            r'[àáâãäå]': 'a',
            r'[èéêë]': 'e',
            r'[ìíîï]': 'i',
            r'[òóôõö]': 'o',
            r'[ùúûü]': 'u',
            r'[ç]': 'c',
            r'[ñ]': 'n'
        }
        
        for pattern, replacement in corrections.items():
            text = re.sub(pattern, replacement, text)
        
        # Nettoyage des espaces et formatage
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[\r\n]+', '\n', text)
        text = re.sub(r'\n\s*\n', '\n', text)
        
        return text.strip()

    def extract_bank_info(self, text: str) -> Dict[str, Any]:
        """Extraction spécialisée des informations bancaires"""
        logger.info("🏛️ Extraction informations bancaires...")
        
        bank_info = {
            "nom_banque": "",
            "forme_juridique": "",
            "capital_social": "",
            "numero_rc": "",
            "numero_tva": "",
            "adresse_siege": "",
            "telephone": "",
            "fax": "",
            "email": "",
            "site_web": "",
            "code_banque": "",
            "identifiant_unique": ""
        }
        
        # Patterns optimisés pour informations bancaires
        bank_patterns = {
            "nom_banque": [
                r'BANQUE\s+TUNISO[\-\s]?KOWEITIENNE',
                r'Banque\s+Tuniso[\-\s]?Koweitienne',
                r'BTK(?:\s|$)',
                r'B\.T\.K\.?'
            ],
            "forme_juridique": [
                r'(S\.A\.?)(?:\s|$)',
                r'(Société\s+Anonyme)',
                r'(SARL)',
                r'Forme\s+juridique\s*:?\s*([A-Z\.]+)'
            ],
            "capital_social": [
                r'capital\s+(?:social\s+)?de\s+([0-9\s,\.]+)\s*(?:dinars?|DT|D)',
                r'au\s+capital\s+de\s+([0-9\s,\.]+)',
                r'Capital\s*:?\s*([0-9\s,\.]+)\s*(?:dinars?|DT|D)',
                r'([0-9]{3}[\s,\.][0-9]{3}[\s,\.][0-9]{3})\s*(?:dinars?|DT|D)'
            ],
            "numero_rc": [
                r'R\.?C\.?\s*[=:]\s*(B[0-9]+)',
                r'RC\s*[=:]\s*(B[0-9]+)',
                r'Registre.*?Commerce.*?([B][0-9]{9,12})',
                r'\b(B[0-9]{9,12})\b'
            ],
            "numero_tva": [
                r'TVA\s*[=:]\s*([0-9]+)',
                r'T\.V\.A\s*[=:]\s*([0-9]+)',
                r'Numéro.*?TVA.*?([0-9]{5,})',
                r'Identifiant.*?TVA.*?([0-9]{5,})'
            ],
            "adresse_siege": [
                r'([0-9]+\s+bis,?\s+(?:Av\.?|Avenue)\s+Mohamed\s+V[^|]{10,100})',
                r'(Avenue\s+Mohamed\s+V.*?(?:Tunis|1001))',
                r'([0-9]+\s+bis.*?(?:Tunis|1001|B\.P\.))',
                r'Siège\s+social\s*:?\s*([^|]{20,100})',
                r'Adresse\s*:?\s*([0-9]+.*?(?:Tunis|1001))'
            ],
            "telephone": [
                r'Tél\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})',
                r'Téléphone\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})',
                r'Tel\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})'
            ],
            "fax": [
                r'Fax\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})',
                r'Télécopie\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})'
            ],
            "site_web": [
                r'(www\.btknet\.com)',
                r'Site.*?(www\.btknet\.com)',
                r'Internet\s*:?\s*(www\.[a-z\.]+)'
            ],
            "identifiant_unique": [
                r'Identifiant\s+Unique\s+n°\s*([0-9]+)',
                r'ID\s+Unique\s*:?\s*([0-9]+)',
                r'Code\s+Identifiant\s*:?\s*([0-9]+)'
            ]
        }
        
        # Extraction avec validation
        for field, patterns in bank_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    value = match.group(1).strip() if match.groups() else match.group(0).strip()
                    if value and self.validate_bank_field(value, field):
                        bank_info[field] = value
                        break
                if bank_info[field]:
                    break
        
        return bank_info

    def extract_contract_info(self, text: str) -> Dict[str, Any]:
        """Extraction spécialisée des informations contractuelles"""
        logger.info("📋 Extraction informations contractuelles...")
        
        contract_info = {
            "type_contrat": "",
            "numero_contrat": "",
            "date_edition": "",
            "date_signature": "",
            "date_echeance": "",
            "montant_principal": "",
            "devise": "",
            "duree": "",
            "taux_interet": "",
            "taux_effectif_global": "",
            "commission_gestion": "",
            "commission_engagement": "",
            "frais_dossier": "",
            "garanties": "",
            "modalites_remboursement": "",
            "periodicite": "",
            "objet_financement": ""
        }
        
        # Patterns optimisés pour informations contractuelles
        contract_patterns = {
            "type_contrat": [
                r'CONTRAT\s+DE\s+(PRET|PRÊT|CREDIT|CRÉDIT)',
                r'Convention\s+de\s+([A-Za-z\s]+)',
                r'(CONTRAT\s+DE\s+(?:PRET|CREDIT))',
                r'Type\s+(?:de\s+)?contrat\s*:?\s*([A-Z\s]+)',
                r'Nature\s+du\s+contrat\s*:?\s*([A-Z\s]+)'
            ],
            "numero_contrat": [
                r'Numéro.*?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)',
                r'Référence\s*[=:]\s*([A-Z0-9\-\/]+)',
                r'N°.*?(?:contrat|prêt)\s*[=:]?\s*([A-Z0-9\-\/]{6,})',
                r'Contrat\s+(?:n°|numéro)\s*[=:]?\s*([A-Z0-9\-\/]+)'
            ],
            "date_edition": [
                r'Edité\s+le\s*:?\s*([0-9]{1,2}[\/\-\.][0-9]{1,2}[\/\-\.][0-9]{4})',
                r'Date\s+d\'édition\s*:?\s*([0-9]{1,2}[\/\-\.][0-9]{1,2}[\/\-\.][0-9]{4})',
                r'Etabli\s+le\s*:?\s*([0-9]{1,2}[\/\-\.][0-9]{1,2}[\/\-\.][0-9]{4})'
            ],
            "montant_principal": [
                r'somme\s+principale\s+(?:de\s+)?([0-9\s,\.]+)\s*(?:dinars?|DT|D)',
                r'montant\s+(?:total|du\s+prêt).*?([0-9\s,\.]+)\s*(?:dinars?|DT|D)',
                r'prêt\s+de\s+([0-9\s,\.]+)\s*(?:dinars?|DT|D)',
                r'Capital\s+emprunté\s*:?\s*([0-9\s,\.]+)'
            ],
            "duree": [
                r'durée\s+de\s+([0-9]+\s+(?:mois|ans?))',
                r'consenti\s+pour.*?([0-9]+\s+(?:mois|ans?))',
                r'période\s+de\s+([0-9]+\s+[a-z]+)',
                r'Durée\s*:?\s*([0-9]+\s+(?:mois|ans?))'
            ],
            "taux_interet": [
                r'taux\s+(?:d\'intérêt\s+)?(?:annuel\s+)?(?:de\s+)?([0-9,\.]+\s*%)',
                r'intérêts\s+au\s+taux\s+(?:de\s+)?([0-9,\.]+\s*%)',
                r'Taux\s*:?\s*([0-9,\.]+\s*%)'
            ],
            "garanties": [
                r'garanties?\s*:?\s*([^.]{10,150})',
                r'caution.*?([^.]{10,150})',
                r'Sûretés\s*:?\s*([^.]{10,150})'
            ]
        }
        
        # Extraction avec validation
        for field, patterns in contract_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    value = match.group(1).strip() if match.groups() else match.group(0).strip()
                    if value and self.validate_contract_field(value, field):
                        contract_info[field] = value
                        break
                if contract_info[field]:
                    break
        
        return contract_info

    def extract_client_info(self, text: str) -> Dict[str, Any]:
        """Extraction spécialisée des informations client"""
        logger.info("👤 Extraction informations client...")
        
        client_info = {
            "code_client": "",
            "nom": "",
            "prenom": "",
            "nom_complet": "",
            "date_naissance": "",
            "lieu_naissance": "",
            "nationalite": "",
            "situation_familiale": "",
            "nombre_enfants": "",
            "profession": "",
            "secteur_activite": "",
            "employeur": "",
            "revenus_mensuels": "",
            "adresse_domicile": "",
            "ville": "",
            "code_postal": "",
            "pays": "",
            "telephone_fixe": "",
            "telephone_mobile": "",
            "email": "",
            "cin": "",
            "passeport": "",
            "permis_conduire": "",
            "numero_compte": "",
            "agence": ""
        }
        
        # Patterns optimisés pour informations client
        client_patterns = {
            "code_client": [
                r'Code\s+Client\s*:?\s*([A-Z0-9X]{3,15})',
                r'Client\s+(?:n°|numéro)\s*:?\s*([A-Z0-9]{6,})',
                r'Identifiant\s+client\s*:?\s*([A-Z0-9]{6,})'
            ],
            "nom": [
                r'Nom\s+(?:du\s+)?(?:Client|Emprunteur)\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,40})',
                r'Nom\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,40})',
                r'Emprunteur\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,40})'
            ],
            "prenom": [
                r'Prénom\s+(?:du\s+)?(?:Client|Emprunteur)\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,40})',
                r'Prénom\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,40})'
            ],
            "date_naissance": [
                r'Date\s+de\s+Naissance\s*:?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Né(?:e)?\s+le\s*:?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Naissance\s*:?\s*([0-9X\/\-\.]{6,12})'
            ],
            "nationalite": [
                r'Nationalité\s*:?\s*([A-ZÀ-Ÿ]{2,25})',
                r'Nat\.\s*:?\s*([A-ZÀ-Ÿ]{2,25})',
                r'Pays\s+de\s+nationalité\s*:?\s*([A-ZÀ-Ÿ]{2,25})'
            ],
            "situation_familiale": [
                r'Situation\s+Familiale\s*:?\s*([A-Za-zÀ-ÿ]{2,25})',
                r'Régime\s+Matrimonial\s*:?\s*([A-Za-zÀ-ÿ]{2,25})',
                r'Etat\s+civil\s*:?\s*([A-Za-zÀ-ÿ]{2,25})'
            ],
            "profession": [
                r'Profession\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,50})',
                r'Emploi\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,50})',
                r'Activité\s*:?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,50})'
            ],
            "telephone_mobile": [
                r'Mobile\s*:?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Téléphone\s+(?:mobile|portable)\s*:?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'GSM\s*:?\s*([0-9X\s\.\-\+\(\)]{8,25})'
            ],
            "adresse_domicile": [
                r'Adresse\s*:?\s*([^|]{15,120})',
                r'Domicile\s*:?\s*([^|]{15,120})',
                r'Résidence\s*:?\s*([^|]{15,120})'
            ],
            "cin": [
                r'CIN\s*:?\s*([0-9X]{8})',
                r'Carte\s+d\'identité\s*:?\s*([0-9X]{8})',
                r'N°\s+CIN\s*:?\s*([0-9X]{8})'
            ],
            "numero_compte": [
                r'Compte\s*(?:n°|numéro)?\s*:?\s*([0-9\-]{10,20})',
                r'N°\s+Compte\s*:?\s*([0-9\-]{10,20})',
                r'Numéro\s+de\s+compte\s*:?\s*([0-9\-]{10,20})'
            ]
        }
        
        # Extraction avec validation
        for field, patterns in client_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    value = match.group(1).strip() if match.groups() else match.group(0).strip()
                    if value and self.validate_client_field(value, field):
                        client_info[field] = value
                        break
                if client_info[field]:
                    break
        
        return client_info

    def validate_bank_field(self, value: str, field: str) -> bool:
        """Validation spécialisée pour champs bancaires"""
        if not value or len(value.strip()) < 1:
            return False
            
        # Validations spécifiques
        if field == "capital_social" and not re.match(r'^[0-9\s,\.]+$', value.replace(' ', '')):
            return False
        if field == "numero_rc" and not re.match(r'^B[0-9]{9,12}$', value):
            return False
        if field == "numero_tva" and not re.match(r'^[0-9]{5,}$', value):
            return False
        if field == "telephone" and not re.match(r'^[0-9\s\.\-\+\(\)]{6,20}$', value):
            return False
            
        return True

    def validate_contract_field(self, value: str, field: str) -> bool:
        """Validation spécialisée pour champs contractuels"""
        if not value or len(value.strip()) < 1:
            return False
            
        # Validations spécifiques
        if field == "montant_principal" and not re.match(r'^[0-9\s,\.]+$', value.replace(' ', '')):
            return False
        if field == "taux_interet" and not re.match(r'^[0-9,\.]+\s*%?$', value):
            return False
        if field == "date_edition" and not re.match(r'^[0-9]{1,2}[\/\-\.][0-9]{1,2}[\/\-\.][0-9]{4}$', value):
            return False
            
        return True

    def validate_client_field(self, value: str, field: str) -> bool:
        """Validation spécialisée pour champs client"""
        if not value or len(value.strip()) < 1:
            return False
            
        # Validations spécifiques
        if field == "cin" and not re.match(r'^[0-9X]{8}$', value):
            return False
        if field == "date_naissance" and not re.match(r'^[0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4}$', value):
            return False
        if field == "telephone_mobile" and not re.match(r'^[0-9X\s\.\-\+\(\)]{6,25}$', value):
            return False
            
        return True

    def detect_masked_fields(self, text: str) -> List[Dict]:
        """Détection avancée des champs masqués"""
        masked_fields = []
        
        masked_patterns = [
            (r'Code\s+Client\s*:?\s*(X+)', 'code_client'),
            (r'Nom.*?:?\s*(X{2,})', 'nom'),
            (r'Prénom.*?:?\s*(X{2,})', 'prenom'),
            (r'Date.*?Naissance\s*:?\s*(X+)', 'date_naissance'),
            (r'CIN\s*:?\s*(X+)', 'cin'),
            (r'Adresse.*?:?\s*(X{3,})', 'adresse'),
            (r'Mobile\s*:?\s*(X+)', 'telephone_mobile'),
            (r'Profession\s*:?\s*(X+)', 'profession'),
            (r'Nationalité\s*:?\s*(X+)', 'nationalite')
        ]
        
        for pattern, field_name in masked_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                mask_value = match.group(1)
                masked_fields.append({
                    "champ": field_name,
                    "masque": mask_value,
                    "longueur_masque": len(mask_value),
                    "position": match.start(),
                    "contexte": text[max(0, match.start()-20):match.end()+20].strip()
                })
        
        return masked_fields

    def calculate_quality_scores(self, bank_info: Dict, contract_info: Dict, client_info: Dict, masked_fields: List) -> Dict:
        """Calcul des scores de qualité d'extraction"""
        
        # Comptage des champs remplis
        bank_filled = sum(1 for v in bank_info.values() if v and v.strip())
        contract_filled = sum(1 for v in contract_info.values() if v and v.strip())
        client_filled = sum(1 for v in client_info.values() if v and v.strip())
        
        total_filled = bank_filled + contract_filled + client_filled
        total_possible = len(bank_info) + len(contract_info) + len(client_info)
        
        # Calcul des scores
        bank_score = (bank_filled / len(bank_info)) * 100 if bank_info else 0
        contract_score = (contract_filled / len(contract_info)) * 100 if contract_info else 0
        client_score = (client_filled / len(client_info)) * 100 if client_info else 0
        global_score = (total_filled / total_possible) * 100 if total_possible > 0 else 0
        
        return {
            "score_global": round(global_score, 1),
            "score_banque": round(bank_score, 1),
            "score_contrat": round(contract_score, 1),
            "score_client": round(client_score, 1),
            "champs_remplis_total": total_filled,
            "champs_possibles_total": total_possible,
            "champs_banque_remplis": bank_filled,
            "champs_contrat_remplis": contract_filled,
            "champs_client_remplis": client_filled,
            "champs_masques_detectes": len(masked_fields),
            "taux_completion": f"{total_filled}/{total_possible}"
        }

    def process_contract(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement complet et optimisé d'un contrat"""
        logger.info(f"🚀 TRAITEMENT AVANCÉ: {pdf_name}")
        
        try:
            # 1. Extraction OCR
            logger.info("📄 Phase 1: Extraction OCR avancée...")
            ocr_text = self.extract_text_from_pdf(pdf_path, pdf_name)
            
            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")
            
            logger.info(f"📝 Texte extrait: {len(ocr_text)} caractères")
            
            # 2. Extraction spécialisée par section
            logger.info("🔍 Phase 2: Extraction spécialisée...")
            
            bank_info = self.extract_bank_info(ocr_text)
            contract_info = self.extract_contract_info(ocr_text)
            client_info = self.extract_client_info(ocr_text)
            
            # 3. Détection des champs masqués
            logger.info("🎭 Phase 3: Détection champs masqués...")
            masked_fields = self.detect_masked_fields(ocr_text)
            
            # 4. Calcul des scores de qualité
            logger.info("📊 Phase 4: Calcul scores qualité...")
            quality_scores = self.calculate_quality_scores(bank_info, contract_info, client_info, masked_fields)
            
            # 5. Assemblage du résultat final
            result = {
                "informations_banque": bank_info,
                "informations_contrat": contract_info,
                "informations_client": client_info,
                "champs_masques": masked_fields,
                "scores_qualite": quality_scores,
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Advanced Contract Extractor v2.0",
                    "ocr_text_length": len(ocr_text),
                    "pages_traitees": ocr_text.count("--- Page"),
                    "version": "2.0"
                }
            }
            
            # 6. Affichage des résultats
            logger.info("✅ EXTRACTION TERMINÉE!")
            logger.info(f"📊 Score global: {quality_scores['score_global']}%")
            logger.info(f"🏛️ Banque: {quality_scores['champs_banque_remplis']} champs")
            logger.info(f"📋 Contrat: {quality_scores['champs_contrat_remplis']} champs")
            logger.info(f"👤 Client: {quality_scores['champs_client_remplis']} champs")
            logger.info(f"🎭 Masqués: {len(masked_fields)} champs")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erreur: {str(e)}")
            return {
                "erreur": str(e),
                "informations_banque": {},
                "informations_contrat": {},
                "informations_client": {},
                "champs_masques": [],
                "scores_qualite": {"score_global": 0},
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Advanced Contract Extractor v2.0 (ERROR)",
                    "erreur": str(e)
                }
            }

def main():
    """Fonction principale améliorée"""
    logger.info("🎯 === EXTRACTEUR DE CONTRATS AVANCÉ V2.0 ===")
    logger.info("🚀 EXTRACTION INTELLIGENTE PAR SECTIONS")
    
    # Initialisation
    extractor = AdvancedContractExtractor()
    
    # Vérifications
    if not os.path.exists(PDF_FOLDER):
        logger.error(f"❌ Dossier {PDF_FOLDER} non trouvé")
        return
    
    # Création des dossiers
    for folder in [IMG_FOLDER, TEXT_FOLDER, RESULT_FOLDER]:
        os.makedirs(folder, exist_ok=True)
    
    # Recherche des PDFs
    pdf_files = [f for f in os.listdir(PDF_FOLDER) 
                 if f.lower().endswith('.pdf') and not f.startswith('~')]
    
    if not pdf_files:
        logger.error(f"❌ Aucun PDF trouvé dans {PDF_FOLDER}")
        return
    
    logger.info(f"📁 {len(pdf_files)} fichier(s) PDF à traiter")
    
    # Traitement
    results_summary = []
    
    for i, pdf_file in enumerate(pdf_files, 1):
        logger.info(f"\n{'='*80}")
        logger.info(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        logger.info(f"{'='*80}")
        
        pdf_path = os.path.join(PDF_FOLDER, pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]
        
        # Traitement
        result = extractor.process_contract(pdf_path, pdf_name)
        
        # Sauvegarde
        output_path = f"{RESULT_FOLDER}/{pdf_name}_ADVANCED.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Sauvegardé: {output_path}")
        
        # Ajout au résumé
        if "scores_qualite" in result:
            results_summary.append({
                "fichier": pdf_file,
                "score": result["scores_qualite"]["score_global"],
                "champs_total": result["scores_qualite"]["champs_remplis_total"]
            })
    
    # Résumé final
    logger.info(f"\n{'='*80}")
    logger.info("🏁 EXTRACTION TERMINÉE - RÉSUMÉ")
    logger.info(f"{'='*80}")
    
    if results_summary:
        avg_score = sum(r["score"] for r in results_summary) / len(results_summary)
        total_fields = sum(r["champs_total"] for r in results_summary)
        
        logger.info(f"📊 Score moyen: {avg_score:.1f}%")
        logger.info(f"📋 Total champs extraits: {total_fields}")
        logger.info(f"📁 Fichiers traités: {len(results_summary)}")

if __name__ == "__main__":
    main()