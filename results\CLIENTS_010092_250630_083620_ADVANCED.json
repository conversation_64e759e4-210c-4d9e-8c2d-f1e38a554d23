{"informations_banque": {"nom_banque": "", "forme_juridique": "S.A", "capital_social": "100 000 000", "numero_rc": "B152691906", "numero_tva": "12357", "adresse_siege": "10 bis , Av. <PERSON>. B.P.", "telephone": "", "fax": "71343 106", "email": "", "site_web": "www.btknet.com", "code_banque": "", "identifiant_unique": ""}, "informations_contrat": {"type_contrat": "", "numero_contrat": "", "date_edition": "", "date_signature": "", "date_echeance": "", "montant_principal": "", "devise": "", "duree": "", "taux_interet": "", "taux_effectif_global": "", "commission_gestion": "", "commission_engagement": "", "frais_dossier": "", "garanties": "", "modalites_remboursement": "", "periodicite": "", "objet_financement": ""}, "informations_client": {"code_client": "XXXX", "nom": "XXXX Prenom du Client XXXXKX Informations", "prenom": "", "nom_complet": "", "date_naissance": "", "lieu_naissance": "", "nationalite": "", "situation_familiale": "oe", "nombre_enfants": "", "profession": "XXXXX XX Type XX Frequence Segment XXXX Montant XXX", "secteur_activite": "", "employeur": "", "revenus_mensuels": "", "adresse_domicile": "Courrier Adresse Permanente XXXX XXKXX Emploi Budget Profession XXXXX XX Type XX Frequence Segment XXXX Montant XXX Devi", "ville": "", "code_postal": "", "pays": "", "telephone_fixe": "", "telephone_mobile": "", "email": "", "cin": "", "passeport": "", "permis_conduire": "", "numero_compte": "", "agence": ""}, "champs_masques": [{"champ": "code_client", "masque": "XXXX", "longueur_masque": 4, "position": 90, "contexte": "ormations du Client Code Client XXXX Intitule XXXKX Nom"}, {"champ": "nom", "masque": "XXXX", "longueur_masque": 4, "position": 122, "contexte": "XXXX Intitule XXXKX Nom du Client XXXX Prenom du Client XX"}, {"champ": "nom", "masque": "XXXX", "longueur_masque": 4, "position": 144, "contexte": "m du Client XXXX Prenom du Client XXXXKX Informations de N"}, {"champ": "date_naissance", "masque": "XXXXX", "longueur_masque": 5, "position": 225, "contexte": "XXXKXXK Pays XXKXX Date de Naissance XXXXX Lieu de Naissance X"}, {"champ": "adresse", "masque": "XXXX", "longueur_masque": 4, "position": 492, "contexte": "u Client Mobile XXX Adresse Courrier Adresse Permanente XXXX XXKXX Emploi Budget"}, {"champ": "telephone_mobile", "masque": "XXX", "longueur_masque": 3, "position": 481, "contexte": "elephones du Client Mobile XXX Adresse Courrier Ad"}, {"champ": "profession", "masque": "XXXXX", "longueur_masque": 5, "position": 553, "contexte": "XXKXX Emploi Budget Profession XXXXX XX Type XX Frequenc"}], "scores_qualite": {"score_global": 22.2, "score_banque": 58.3, "score_contrat": 0.0, "score_client": 20.0, "champs_remplis_total": 12, "champs_possibles_total": 54, "champs_banque_remplis": 7, "champs_contrat_remplis": 0, "champs_client_remplis": 5, "champs_masques_detectes": 7, "taux_completion": "12/54"}, "metadata": {"fichier_source": "CLIENTS_010092_250630_083620.pdf", "extraction_timestamp": "2025-07-28T17:09:11.698836", "methode_extraction": "Advanced Contract Extractor v2.0", "ocr_text_length": 1282, "pages_traitees": 1, "version": "2.0"}}